# Git
.git
.gitignore

# Documentation
README.md
CONTRIBUTING.md
LICENSE
*.md

# Build artifacts
build/
dist/
*.tar.gz
*.zip

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Test files
*_test.go
testdata/

# Examples (optional - remove if you want examples in container)
examples/

# Config files (should be mounted at runtime)
config.yaml
config.yml
*.env

# Temporary files
tmp/
temp/
*.tmp

# CI/CD
.github/

# Dependencies (will be downloaded during build)
vendor/