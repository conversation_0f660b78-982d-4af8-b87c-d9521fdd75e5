### The Core Idea

We will make the `VaultClient` responsible for its own logging by giving it a logger instance when it's created. This means we need to change where and when the `VaultClient` is created.

---

### Step 1: Refactor `VaultClient` to Hold a Logger

First, let's fix `internal/config/vault.go` so it's prepared to accept a logger.

**File: `internal/config/vault.go`**

**1. Update the `VaultClient` struct:** Add a field to hold the logger.

```go
// Before
type VaultClient struct {
	client *api.Client
	config VaultConfig
}

// After
type VaultClient struct {
	client *api.Client
	config VaultConfig
	logger *errors.Logger // Add this field
}
```

**2. Update the `NewVaultClient` constructor:** Change its signature to accept a logger and use it instead of the global one.

```go
// Before
func NewVaultClient(config VaultConfig) (*VaultClient, error) {
    if !config.Enabled {
        if errors.GlobalLogger != nil { // <-- Global logger usage
            errors.GlobalLogger.Debug("Vault integration disabled")
        }
        return nil, nil
    }
    // ... more GlobalLogger usage ...
    return &VaultClient{
		client: client,
		config: config,
	}, nil
}

// After
func NewVaultClient(config VaultConfig, logger *errors.Logger) (*VaultClient, error) {
    if !config.Enabled {
        if logger != nil { // <-- Use passed-in logger
            logger.Debug("Vault integration disabled")
        }
        return nil, nil
    }
    // ...
    // Replace ALL instances of errors.GlobalLogger with logger
    // ...
    if logger != nil {
        logger.Info("Successfully connected to Vault",
            "address", vaultConfig.Address,
            "version", health.Version,
            "sealed", health.Sealed,
            "cluster_name", health.ClusterName)
    }

    return &VaultClient{
		client: client,
		config: config,
		logger: logger, // <-- Store the logger in the struct
	}, nil
}
```

**3. Update `VaultClient` methods:** Change methods to use the logger stored in the struct (`vc.logger`).

```go
// Before (example from GetStringSecret)
func (vc *VaultClient) GetStringSecret(path, key string) (string, error) {
    // ...
    if errors.GlobalLogger != nil { // <-- Global logger usage
        errors.GlobalLogger.Debug("String secret retrieved from Vault", ...)
    }
    return strValue, nil
}

// After
func (vc *VaultClient) GetStringSecret(path, key string) (string, error) {
    // ...
    if vc.logger != nil { // <-- Use the struct's logger
        vc.logger.Debug("String secret retrieved from Vault", ...)
    }
    return strValue, nil
}
```
*Apply this change to all methods on `VaultClient` that use `GlobalLogger`.*

---

### Step 2: Separate Config Loading from Secret Application

This is the key architectural change. `LoadConfig` should only load from files/env. Applying secrets from Vault should be a separate, explicit step that happens in `main`.

**File: `internal/config/config.go`**

**1. Modify `LoadSecretsFromVault`:** Change its signature to accept a logger. It will now be called from `main`, not from `LoadConfig`.

```go
// Before
func LoadSecretsFromVault(config *Config, vaultConfig VaultConfig) error {
    // ... uses errors.GlobalLogger
    client, err := NewVaultClient(vaultConfig) // <-- This call will now fail
    // ...
}

// After
func ApplyVaultSecrets(config *Config, logger *errors.Logger) error {
    vaultConfig := config.Vault
    if !vaultConfig.Enabled {
        return nil // Vault not enabled, nothing to do.
    }

    logger.Info("Applying secrets from Vault...")

    // Now we can create the client because we have the logger!
    client, err := NewVaultClient(vaultConfig, logger)
    if err != nil {
        return fmt.Errorf("failed to initialize vault client: %w", err)
    }
    if client == nil {
        return nil // Not an error, just disabled.
    }

    // ... (rest of the logic from the old LoadSecretsFromVault)
    // ... but change NewVaultClient call to pass the logger.
    // ... and remove all GlobalLogger calls, as they are now inside VaultClient.
    
    // Example change inside this function:
    if vaultConfig.Secrets.GeminiKey != "" {
        geminiKey, err := client.GetStringSecret(vaultConfig.Secrets.GeminiKey, "api_key")
        if err != nil {
            // The logger is now available here if you need it for the top-level logic
            logger.LogError(err, "Failed to load Gemini API key from Vault", "path", vaultConfig.Secrets.GeminiKey)
            return fmt.Errorf("failed to load Gemini API key from vault: %w", err)
        }
        // ...
    }
    
    logger.Info("Successfully completed applying secrets from Vault")
    return nil
}
```

**2. Update `LoadConfig`:** Remove the call to `LoadSecretsFromVault`.

```go
// Before
func LoadConfig() (*Config, error) {
    // ...
    // Load secrets from Vault if enabled
	if err := LoadSecretsFromVault(&config, config.Vault); err != nil {
		return nil, fmt.Errorf("failed to load secrets from vault: %w", err)
	}
    // ...
    return &config, nil
}

// After
func LoadConfig() (*Config, error) {
    // ...
    // The call to LoadSecretsFromVault is REMOVED from this function.
    // ...
    return &config, nil
}
```

---

### Step 3: Update the Startup Sequence in `main`

Now, tie it all together in `main.go`.

**File: `cmd/resumatter/main.go`**

```go
// Before
func main() {
    // ...
	cfg, err := config.LoadConfig()
	if err != nil {
        // ...
    }
	if err := errors.InitLogger(cfg.App.LogLevel); err != nil {
        // ...
    }
    // ...
    if err := cli.Execute(ctx, cfg); err != nil {
        // ...
    }
}

// After
func main() {
    // ...
    // Step 1: Load config from files and environment variables.
	cfg, err := config.LoadConfig()
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to load configuration: %v\n", err)
		os.Exit(1)
	}

    // Step 2: Create the logger using the loaded config.
	logger, err := errors.New(cfg.App.LogLevel)
	if err != nil {
		fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
		os.Exit(1)
	}

    // Step 3: Apply secrets from Vault, now that we have both config and logger.
    if err := config.ApplyVaultSecrets(cfg, logger); err != nil {
        logger.LogError(err, "Failed to apply secrets from Vault")
        os.Exit(1)
    }

	logger.Info("Starting resumatter application",
        // ...
    )

    // Step 4: Execute the application, passing both dependencies.
	if err := cli.Execute(ctx, cfg, logger); err != nil {
		logger.LogError(err, "Application execution failed")
		os.Exit(1)
	}
}
```

### Summary of the Fix

1.  **Modify `VaultClient`:** Make it hold its own `*errors.Logger` instance.
2.  **Modify `NewVaultClient`:** Make it accept a logger and store it.
3.  **Separate Concerns:** Create a new function `config.ApplyVaultSecrets` that accepts both the config and the logger. Move the logic from the old `LoadSecretsFromVault` into it.
4.  **Update `LoadConfig`:** Remove the call to load Vault secrets. Its job is now simpler.
5.  **Update `main`:** Orchestrate the new startup sequence: Load Config -> Create Logger -> Apply Vault Secrets -> Run App.

