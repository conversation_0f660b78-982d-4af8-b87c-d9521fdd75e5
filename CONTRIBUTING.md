# Contributing to <PERSON><PERSON><PERSON><PERSON>

Thank you for your interest in contributing to Resumatter! We welcome contributions from the community.

## Maintainer Availability

**Please note:** This project is maintained by a small team with limited availability (typically 1-2 times per week). 

- **Response time**: Expect 3-7 days for initial responses to issues and PRs
- **Review cycles**: Pull requests may take 1-2 weeks for review and feedback
- **Release schedule**: New releases are published as needed, typically monthly

We appreciate your patience and understanding!

## How to Contribute

### Reporting Issues

Before creating an issue, please:
1. **Search existing issues** to avoid duplicates
2. **Use the issue templates** when available
3. **Provide clear reproduction steps** for bugs
4. **Include relevant system information** (OS, Go version, etc.)

### Suggesting Features

For feature requests:
- **Explain the use case** and problem you're solving
- **Describe the proposed solution** in detail
- **Consider backward compatibility** implications
- **Be patient** - feature development may take time due to limited maintainer availability

### Pull Requests

We welcome pull requests! To increase chances of acceptance:

#### Before You Start
- **Open an issue first** for significant changes to discuss the approach
- **Check existing PRs** to avoid duplicate work
- **Start small** - smaller PRs are easier to review and merge

#### PR Guidelines
1. **Fork the repository** and create a feature branch
2. **Write clear commit messages** following conventional commits format (no emoji)
3. **Include tests** for new functionality
4. **Update documentation** if needed
5. **Run the full test suite** before submitting
6. **Keep PRs focused** - one feature/fix per PR

#### Code Standards
- Follow existing Go conventions and patterns
- Run `make lint` and `make vet` before submitting
- Run `make security` to check for security issues
- Ensure all tests pass with `make test`
- **Optional but recommended**: Run `modernize -test ./...` to check for modernization opportunities
- Add appropriate error handling and logging
- Update relevant documentation
- **No emoji in code or commit messages** (maintainer preference)

### Development Setup

```bash
# Clone your fork
git clone https://github.com/your-username/resumatter.git
cd resumatter

# Install dependencies
make deps

# Setup development tools (optional but recommended)
make dev-setup

# Build and test
make build
make test

# Run linting and security checks
make lint
make security

# Optional: Check for modernization opportunities
modernize -test ./...
```

### Testing

- **Unit tests**: Add tests for new functions and methods
- **Integration tests**: Test end-to-end functionality when applicable
- **Manual testing**: Test with real resume/job description files
- **Example verification**: Ensure examples in README still work

### Documentation

When contributing:
- Update README.md if adding new features
- Add inline code comments for complex logic
- Update configuration examples if needed
- Consider adding examples for new functionality

## Types of Contributions Welcomed

Given our limited maintenance capacity, we especially appreciate:

### High Priority
- **Bug fixes** with clear reproduction steps
- **Documentation improvements** and typo fixes
- **Test coverage improvements**
- **Performance optimizations**

### Medium Priority
- **New output formats** (following existing patterns)
- **Configuration enhancements**
- **Example additions** for different industries

### Lower Priority (Due to Complexity)
- **New AI provider integrations** (requires significant testing)
- **Major architectural changes** (need extensive discussion)
- **Breaking API changes** (require careful planning)

## Code of Conduct

- **Be respectful** and constructive in all interactions
- **Focus on the code and ideas**, not personal attributes
- **Help create a welcoming environment** for all contributors
- **Be patient** with review times given maintainer availability

## Getting Help

- **GitHub Issues**: For bugs and feature requests
- **GitHub Discussions**: For questions and general discussion
- **Documentation**: Check README.md and config.example.yaml first

## Recognition

All contributors will be acknowledged in release notes and we maintain a contributors list. Significant contributions may result in maintainer privileges for active, trusted contributors.

## License

By contributing to Resumatter, you agree that your contributions will be licensed under the MIT License.

---

**Thank you for helping make Resumatter better!**

Even small contributions like typo fixes, documentation improvements, or bug reports are valuable and appreciated.