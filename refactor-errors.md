### Why It's Worth Refactoring

1.  **Consistency:** You've already established a clean dependency injection pattern by removing the global config. Applying the same pattern to the logger will make your codebase architecturally consistent.
2.  **Testability:** It's currently difficult to test logging behavior. For example, you can't easily redirect a specific component's log output to a buffer during a test to assert that certain messages were logged. With dependency injection, you can pass a mock logger or a logger writing to an in-memory buffer.
3.  **Clarity:** Function and struct definitions will explicitly declare their need for a logger, making dependencies obvious.
4.  **Flexibility:** In the future, you might want different parts of your application to log to different places or with different formats/levels. Dependency injection makes this trivial, whereas a global logger makes it very difficult.

---

### Refactoring Plan

The plan is nearly identical to the config refactoring: create the logger instance in `main()` and pass it down to the components that need it.

#### Step 1: Eliminate the Global Logger

**File:** `internal/errors/errors.go`

**Before:**
```go
// Global logger instance
var GlobalLogger *Logger

// InitLogger initializes the global logger
func InitLogger(level string) error {
    // ... (implementation)
    GlobalLogger = NewLogger(slogLevel)
    return nil
}
```

**After:**
Delete the `GlobalLogger` variable and rename `InitLogger` to `New` to follow Go conventions for constructors.

```go
// Global logger instance (DELETED)
// var GlobalLogger *Logger

// New initializes a new logger instance. It no longer sets a global variable.
func New(level string) (*Logger, error) {
    var slogLevel slog.Level
    switch level {
    case "debug":
        slogLevel = slog.LevelDebug
    case "info":
        slogLevel = slog.LevelInfo
    case "warn":
        slogLevel = slog.LevelWarn
    case "error":
        slogLevel = slog.LevelError
    default:
        return nil, fmt.Errorf("invalid log level: %s", level)
    }

    // The original NewLogger implementation is now here.
	opts := &slog.HandlerOptions{
		Level: slogLevel,
	}
	handler := slog.NewJSONHandler(os.Stdout, opts)
	logger := slog.New(handler)

	return &Logger{logger: logger}, nil
}
```

#### Step 2: Create and Inject the Logger from `main`

**File:** `cmd/resumatter/main.go`

**Before:**
```go
// ...
if err := errors.InitLogger(cfg.App.LogLevel); err != nil {
    // ...
}
if errors.GlobalLogger != nil {
    errors.GlobalLogger.Info("Starting resumatter application", ...)
}
// ...
```

**After:**
```go
// ...
logger, err := errors.New(cfg.App.LogLevel) // Create the logger instance
if err != nil {
    fmt.Fprintf(os.Stderr, "Failed to initialize logger: %v\n", err)
    os.Exit(1)
}

logger.Info("Starting resumatter application", // Use the local instance
    "version", cli.Version,
    "log_level", cfg.App.LogLevel,
    "ai_provider", cfg.AI.Provider)

// Pass the logger instance alongside the config
if err := cli.Execute(ctx, cfg, logger); err != nil { 
    logger.LogError(err, "Application execution failed")
    os.Exit(1)
}
```

#### Step 3: Propagate the Logger Down the Call Stack

This is the "ripple effect" where you'll update signatures to accept the `*errors.Logger`.

1.  **`internal/cli/root.go`:**
    *   Update `Execute` to accept `logger *errors.Logger`.
    *   Add the logger to the context alongside the config.
    ```go
    // In root.go
    var loggerKey = ... // new private key for logger
    func Execute(ctx context.Context, cfg *config.Config, logger *errors.Logger) error {
        ctx = context.WithValue(ctx, configKey, cfg)
        ctx = context.WithValue(ctx, loggerKey, logger)
        rootCmd.SetContext(ctx)
        return rootCmd.Execute()
    }
    ```

2.  **`internal/ai/` and `internal/server/`:**
    *   Any `New...` function that currently uses `errors.GlobalLogger` will now need to accept a `*errors.Logger` argument.
    *   Structs like `GeminiProvider`, `Server`, `CertificateManager`, `CertWatcher`, etc., will accept the logger in their constructor and store it as a field.

    **Example: `internal/server/http.go`**
    ```go
    // Before: NewServer doesn't know about the logger
    func NewServer(appCfg *config.Config, cfg ServerConfig) *Server { ... }

    // After: NewServer accepts the logger
    func NewServer(appCfg *config.Config, cfg ServerConfig, logger *errors.Logger) *Server {
        // ... store logger in the Server struct
    }
    ```

3.  **Update all call sites:** Everywhere you removed a use of `errors.GlobalLogger`, you will now pass the logger instance that you've plumbed down.

### Optional Refinement: Error Constructors

While you're in `internal/errors/errors.go`, you could slightly refactor the error constructors to be more DRY.

**Before:**
```go
func NewValidationError(code, message string, cause error) *AppError {
	return &AppError{Type: ErrorTypeValidation, ...}
}
func NewIOError(code, message string, cause error) *AppError {
	return &AppError{Type: ErrorTypeIO, ...}
}
// ... and so on
```

**After:**
```go
// Un-exported helper
func newAppError(typ ErrorType, code, message string, cause error) *AppError {
	return &AppError{
		Type:    typ,
		Code:    code,
		Message: message,
		Cause:   cause,
	}
}

// Public constructors are now simple one-liners
func NewValidationError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeValidation, code, message, cause)
}
func NewIOError(code, message string, cause error) *AppError {
	return newAppError(ErrorTypeIO, code, message, cause)
}
// ... and so on
```
This is a minor change but cleans up the code nicely.
