package config

import (
	"resumatter/internal/errors"
	"sync"
)

var (
	loadedPrompts     AllLoadedPrompts
	loadedPromptsOnce sync.Once
)

// LoadedPrompts holds the content of prompts loaded from files
type LoadedPrompts struct {
	SystemPrompts LoadedSystemPrompts
	UserPrompts   LoadedUserPrompts
}

// LoadedSystemPrompts contains loaded system-level instructions
type LoadedSystemPrompts struct {
	TailorResume   string
	EvaluateResume string
	AnalyzeJob     string
}

// LoadedUserPrompts contains loaded user-level prompt templates
type LoadedUserPrompts struct {
	TailorResume   string
	EvaluateResume string
	AnalyzeJob     string
}

// OperationLoadedPrompts holds loaded prompts for a specific operation
type OperationLoadedPrompts struct {
	SystemPrompts LoadedSystemPrompts
	UserPrompts   LoadedUserPrompts
}

// AllLoadedPrompts holds all loaded prompts for all operations
type AllLoadedPrompts struct {
	Global   LoadedPrompts
	Tailor   OperationLoadedPrompts
	Evaluate OperationLoadedPrompts
	Analyze  OperationLoadedPrompts
}

// GetPromptsForOperation returns a copy of the loaded prompts for an operation type
func GetPromptsForOperation(operationType string) OperationLoadedPrompts {
	var result OperationLoadedPrompts

	switch operationType {
	case "tailor":
		result = loadedPrompts.Tailor
		logPromptSource("tailor", &result)
	case "evaluate":
		result = loadedPrompts.Evaluate
		logPromptSource("evaluate", &result)
	case "analyze":
		result = loadedPrompts.Analyze
		logPromptSource("analyze", &result)
	default:
		result = OperationLoadedPrompts{
			SystemPrompts: loadedPrompts.Global.SystemPrompts,
			UserPrompts:   loadedPrompts.Global.UserPrompts,
		}
		logPromptSource("global", &result)
	}

	return result
}

// logPromptSource logs where each prompt came from for debugging purposes
func logPromptSource(operationType string, prompts *OperationLoadedPrompts) {
	// Debug logging removed - no global logger available
	// Prompt source information can be determined via getPromptSource if needed
}

// getPromptSource determines where a prompt came from (file, operation config, global config, or default)
func getPromptSource(operationType, promptType, promptName string) string {
	// Check operation-specific file prompts first
	switch operationType {
	case "tailor":
		if loadedPrompts.Tailor.SystemPrompts.TailorResume != "" && promptType == "system" && promptName == "tailorResume" {
			return "operation-file"
		}
	case "evaluate":
		if loadedPrompts.Evaluate.SystemPrompts.EvaluateResume != "" && promptType == "system" && promptName == "evaluateResume" {
			return "operation-file"
		}
	case "analyze":
		if loadedPrompts.Analyze.SystemPrompts.AnalyzeJob != "" && promptType == "system" && promptName == "analyzeJob" {
			return "operation-file"
		}
	}

	// Check global file prompts
	if loadedPrompts.Global.SystemPrompts.TailorResume != "" && promptType == "system" && promptName == "tailorResume" {
		return "global-file"
	}
	if loadedPrompts.Global.SystemPrompts.EvaluateResume != "" && promptType == "system" && promptName == "evaluateResume" {
		return "global-file"
	}
	if loadedPrompts.Global.SystemPrompts.AnalyzeJob != "" && promptType == "system" && promptName == "analyzeJob" {
		return "global-file"
	}

	// If no file prompts, assume it's from config
	return "config-default"
}
