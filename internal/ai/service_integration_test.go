package ai

import (
	"testing"
	"time"

	"resumatter/internal/config"
)

// Helper functions to create pointers for test values
func timePtr(d time.Duration) *time.Duration { return &d }
func intPtr(i int) *int                      { return &i }
func float32Ptr(f float32) *float32          { return &f }

func TestServiceSpecificCircuitBreakerConfigurations(t *testing.T) {
	// Test that each service (tailor, evaluate, analyze) gets its own circuit breaker
	// configuration as specified in config.example.yaml

	// Set up a mock config with different settings for each operation
	testConfig := &config.Config{
		AI: config.AIConfig{
			// Global defaults
			Provider:    "gemini",
			Model:       "gemini-2.0-flash",
			Timeout:     60 * time.Second,
			APIKey:      "test-key",
			MaxRetries:  3,
			Temperature: 0.7,

			// Operation-specific configurations (matching config.example.yaml)
			Tailor: config.OperationAIConfig{
				Provider:    "gemini",
				Model:       "gemini-2.5-pro",          // More powerful model for tailoring
				Timeout:     timePtr(90 * time.Second), // Longer timeout
				APIKey:      "test-key",
				MaxRetries:  intPtr(2),
				Temperature: float32Ptr(0.3), // Lower temperature for consistency
				CircuitBreaker: config.CircuitBreakerConfig{
					Enabled:          true,
					MaxRequests:      3,
					Interval:         60 * time.Second,
					Timeout:          60 * time.Second,
					MinRequests:      3,
					FailureThreshold: 0.6,
				},
			},

			Evaluate: config.OperationAIConfig{
				Provider:    "gemini",
				Model:       "gemini-2.0-flash-lite",   // Faster model for evaluation
				Timeout:     timePtr(60 * time.Second), // Standard timeout
				APIKey:      "test-key",
				MaxRetries:  intPtr(3),
				Temperature: float32Ptr(0.1), // Very low temperature for factual analysis
				CircuitBreaker: config.CircuitBreakerConfig{
					Enabled:          true,
					MaxRequests:      3,
					Interval:         60 * time.Second,
					Timeout:          60 * time.Second,
					MinRequests:      3,
					FailureThreshold: 0.6,
				},
			},

			Analyze: config.OperationAIConfig{
				Provider:    "gemini",
				Model:       "gemini-1.5-pro",          // More powerful model for analysis
				Timeout:     timePtr(75 * time.Second), // Moderate timeout
				APIKey:      "test-key",
				MaxRetries:  intPtr(2),
				Temperature: float32Ptr(0.2), // Low temperature for consistent analysis
				CircuitBreaker: config.CircuitBreakerConfig{
					Enabled:          true,
					MaxRequests:      3,
					Interval:         60 * time.Second,
					Timeout:          60 * time.Second,
					MinRequests:      3,
					FailureThreshold: 0.6,
				},
			},
		},
	}

	// Test that each service gets created with its specific configuration
	t.Run("TailorServiceConfiguration", func(t *testing.T) {
		tailorConfig := testConfig.GetTailorConfig()
		service, err := NewServiceForOperation(&tailorConfig, "tailor", nil)
		if err != nil {
			// Expected to fail with test API key, but should create service
			t.Logf("Expected error with test API key: %v", err)
			return
		}

		// Verify the service was created with tailor-specific config
		if service.config.Model != "gemini-2.5-pro" {
			t.Errorf("Expected tailor service to use 'gemini-2.5-pro', got '%s'", service.config.Model)
		}
		if *service.config.Timeout != 90*time.Second {
			t.Errorf("Expected tailor service timeout 90s, got %v", *service.config.Timeout)
		}
		if *service.config.Temperature != 0.3 {
			t.Errorf("Expected tailor service temperature 0.3, got %f", *service.config.Temperature)
		}
		if *service.config.MaxRetries != 2 {
			t.Errorf("Expected tailor service max retries 2, got %d", *service.config.MaxRetries)
		}
	})

	t.Run("EvaluateServiceConfiguration", func(t *testing.T) {
		evaluateConfig := testConfig.GetEvaluateConfig()
		service, err := NewServiceForOperation(&evaluateConfig, "evaluate", nil)
		if err != nil {
			// Expected to fail with test API key, but should create service
			t.Logf("Expected error with test API key: %v", err)
			return
		}

		// Verify the service was created with evaluate-specific config
		if service.config.Model != "gemini-2.0-flash-lite" {
			t.Errorf("Expected evaluate service to use 'gemini-2.0-flash-lite', got '%s'", service.config.Model)
		}
		if *service.config.Timeout != 60*time.Second {
			t.Errorf("Expected evaluate service timeout 60s, got %v", *service.config.Timeout)
		}
		if *service.config.Temperature != 0.1 {
			t.Errorf("Expected evaluate service temperature 0.1, got %f", *service.config.Temperature)
		}
		if *service.config.MaxRetries != 3 {
			t.Errorf("Expected evaluate service max retries 3, got %d", *service.config.MaxRetries)
		}
	})

	t.Run("AnalyzeServiceConfiguration", func(t *testing.T) {
		analyzeConfig := testConfig.GetAnalyzeConfig()
		service, err := NewServiceForOperation(&analyzeConfig, "analyze", nil)
		if err != nil {
			// Expected to fail with test API key, but should create service
			t.Logf("Expected error with test API key: %v", err)
			return
		}

		// Verify the service was created with analyze-specific config
		if service.config.Model != "gemini-1.5-pro" {
			t.Errorf("Expected analyze service to use 'gemini-1.5-pro', got '%s'", service.config.Model)
		}
		if *service.config.Timeout != 75*time.Second {
			t.Errorf("Expected analyze service timeout 75s, got %v", *service.config.Timeout)
		}
		if *service.config.Temperature != 0.2 {
			t.Errorf("Expected analyze service temperature 0.2, got %f", *service.config.Temperature)
		}
		if *service.config.MaxRetries != 2 {
			t.Errorf("Expected analyze service max retries 2, got %d", *service.config.MaxRetries)
		}
	})
}

func TestCircuitBreakerIntegrationWithServices(t *testing.T) {
	// Test that services actually use their circuit breaker configurations

	// Create a service with specific circuit breaker config
	testConfig := &config.OperationAIConfig{
		Provider:    "gemini",
		Model:       "test-model",
		Timeout:     timePtr(30 * time.Second),
		APIKey:      "test-key",
		MaxRetries:  intPtr(1),
		Temperature: float32Ptr(0.5),
		CircuitBreaker: config.CircuitBreakerConfig{
			Enabled:          true,
			MaxRequests:      5,
			Interval:         30 * time.Second,
			Timeout:          45 * time.Second,
			MinRequests:      2,
			FailureThreshold: 0.8,
		},
	}

	service, err := NewService(testConfig, nil)
	if err != nil {
		// Expected to fail with test API key, but we can still test the structure
		t.Logf("Expected error with test API key: %v", err)
		return
	}

	// Verify the service has the correct configuration
	if service.config.CircuitBreaker.MaxRequests != 5 {
		t.Errorf("Expected circuit breaker max requests 5, got %d", service.config.CircuitBreaker.MaxRequests)
	}
	if service.config.CircuitBreaker.FailureThreshold != 0.8 {
		t.Errorf("Expected circuit breaker failure threshold 0.8, got %f", service.config.CircuitBreaker.FailureThreshold)
	}

	// Test that the provider has a circuit breaker
	if geminiProvider, ok := service.Provider.(*GeminiProvider); ok {
		stats := geminiProvider.GetCircuitBreakerStats()

		// Check that stats structure is correct
		_, aiExists := stats["ai_operations"].(map[string]any)
		if !aiExists {
			t.Error("AI operations stats should exist")
		}

		_, modelExists := stats["model_operations"].(map[string]any)
		if !modelExists {
			t.Error("Model operations stats should exist")
		}

		// Check overall health
		overallHealthy, healthyExists := stats["overall_healthy"].(bool)
		if !healthyExists {
			t.Error("Overall health status should exist")
		}
		if !overallHealthy {
			t.Error("Circuit breaker should be healthy initially")
		}
	}
}

func TestConfigurationFallbacks(t *testing.T) {
	// Test that operation-specific configs fall back to global configs when not specified

	testConfig := &config.Config{
		AI: config.AIConfig{
			// Global defaults
			Provider:    "gemini",
			Model:       "global-model",
			Timeout:     120 * time.Second,
			APIKey:      "global-key",
			MaxRetries:  5,
			Temperature: 0.9,

			// Tailor config with some values missing (should fall back to global)
			Tailor: config.OperationAIConfig{
				Model: "tailor-specific-model", // Override global
				// Other values should fall back to global
				CircuitBreaker: config.CircuitBreakerConfig{
					Enabled: true,
				},
			},
		},
	}

	tailorConfig := testConfig.GetTailorConfig()

	// Verify fallback behavior
	if tailorConfig.Model != "tailor-specific-model" {
		t.Errorf("Expected tailor-specific model, got '%s'", tailorConfig.Model)
	}
	if tailorConfig.Provider != "gemini" {
		t.Errorf("Expected fallback to global provider 'gemini', got '%s'", tailorConfig.Provider)
	}
	if *tailorConfig.Timeout != 120*time.Second {
		t.Errorf("Expected fallback to global timeout 120s, got %v", *tailorConfig.Timeout)
	}
	if tailorConfig.APIKey != "global-key" {
		t.Errorf("Expected fallback to global API key, got '%s'", tailorConfig.APIKey)
	}
	if *tailorConfig.MaxRetries != 5 {
		t.Errorf("Expected fallback to global max retries 5, got %d", *tailorConfig.MaxRetries)
	}
	if *tailorConfig.Temperature != 0.9 {
		t.Errorf("Expected fallback to global temperature 0.9, got %f", *tailorConfig.Temperature)
	}
}
