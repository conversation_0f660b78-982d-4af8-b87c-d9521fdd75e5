package ai

import (
	"fmt"
	"time"

	"resumatter/internal/config"
	"resumatter/internal/errors"

	"github.com/sony/gobreaker/v2"
	"google.golang.org/genai"
)

// AICircuitBreaker wraps AI operations with circuit breaker pattern
// Now focused on a single operation type
type AICircuitBreaker struct {
	cb *gobreaker.CircuitBreaker[*genai.GenerateContentResponse]
}

// ModelCircuitBreaker wraps model info operations with circuit breaker pattern
type ModelCircuitBreaker struct {
	cb *gobreaker.CircuitBreaker[*genai.Model]
}

// NewAICircuitBreaker creates a new circuit breaker for AI operations with default settings
func NewAICircuitBreaker(logger *errors.Logger) *AICircuitBreaker {
	// Default circuit breaker settings
	defaultSettings := gobreaker.Settings{
		Name:        "AI-Default",
		MaxRequests: 3,
		Interval:    60 * time.Second,
		Timeout:     60 * time.Second,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			// Trip if failure rate >= 60% and we have at least 3 requests
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= 3 && failureRatio >= 0.6
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			logger.Info("Circuit breaker state changed",
				"name", name,
				"from", from.String(),
				"to", to.String())
		},
	}

	return &AICircuitBreaker{
		cb: gobreaker.NewCircuitBreaker[*genai.GenerateContentResponse](defaultSettings),
	}
}

// NewModelCircuitBreaker creates a new circuit breaker for model operations with default settings
func NewModelCircuitBreaker(logger *errors.Logger) *ModelCircuitBreaker {
	// Model info checks are less critical, so be more lenient
	modelSettings := gobreaker.Settings{
		Name:        "AI-ModelInfo",
		MaxRequests: 3,
		Interval:    60 * time.Second,
		Timeout:     60 * time.Second,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= 5 && failureRatio >= 0.8
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			logger.Info("Circuit breaker state changed",
				"name", name,
				"from", from.String(),
				"to", to.String())
		},
	}

	return &ModelCircuitBreaker{
		cb: gobreaker.NewCircuitBreaker[*genai.Model](modelSettings),
	}
}

// NewAICircuitBreakerForOperation creates a circuit breaker configured for a specific operation type
func NewAICircuitBreakerForOperation(operationType string, cfg *config.OperationAIConfig, logger *errors.Logger) *AICircuitBreaker {
	// If circuit breaker is disabled, return nil to indicate no circuit breaker
	if !cfg.CircuitBreaker.Enabled {
		return nil
	}

	settings := gobreaker.Settings{
		Name:        fmt.Sprintf("AI-%s", operationType),
		MaxRequests: cfg.CircuitBreaker.MaxRequests,
		Interval:    cfg.CircuitBreaker.Interval,
		Timeout:     cfg.CircuitBreaker.Timeout,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= cfg.CircuitBreaker.MinRequests &&
				failureRatio >= cfg.CircuitBreaker.FailureThreshold
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			logger.Info("Circuit breaker state changed",
				"name", name,
				"operation_type", operationType,
				"from", from.String(),
				"to", to.String(),
				"max_requests", cfg.CircuitBreaker.MaxRequests,
				"failure_threshold", cfg.CircuitBreaker.FailureThreshold)
		},
	}

	return &AICircuitBreaker{
		cb: gobreaker.NewCircuitBreaker[*genai.GenerateContentResponse](settings),
	}
}

// NewModelCircuitBreakerForOperation creates a model circuit breaker configured for a specific operation type
func NewModelCircuitBreakerForOperation(operationType string, cfg *config.OperationAIConfig, logger *errors.Logger) *ModelCircuitBreaker {
	// If circuit breaker is disabled, return nil to indicate no circuit breaker
	if !cfg.CircuitBreaker.Enabled {
		return nil
	}

	settings := gobreaker.Settings{
		Name:        fmt.Sprintf("AI-Model-%s", operationType),
		MaxRequests: cfg.CircuitBreaker.MaxRequests,
		Interval:    cfg.CircuitBreaker.Interval,
		Timeout:     cfg.CircuitBreaker.Timeout,
		ReadyToTrip: func(counts gobreaker.Counts) bool {
			// Model info is less critical, so use more lenient settings
			failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= 5 && failureRatio >= 0.8
		},
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			logger.Info("Circuit breaker state changed",
				"name", name,
				"operation_type", operationType,
				"from", from.String(),
				"to", to.String(),
				"max_requests", cfg.CircuitBreaker.MaxRequests)
		},
	}

	return &ModelCircuitBreaker{
		cb: gobreaker.NewCircuitBreaker[*genai.Model](settings),
	}
}

// Execute executes the provided function with circuit breaker protection
func (cb *AICircuitBreaker) Execute(fn func() (*genai.GenerateContentResponse, error)) (*genai.GenerateContentResponse, error) {
	if cb == nil || cb.cb == nil {
		// If breaker is disabled/nil, just execute the function directly
		return fn()
	}
	return cb.cb.Execute(fn)
}

// ExecuteModel executes the provided model function with circuit breaker protection
func (cb *ModelCircuitBreaker) ExecuteModel(fn func() (*genai.Model, error)) (*genai.Model, error) {
	if cb == nil || cb.cb == nil {
		// If breaker is disabled/nil, just execute the function directly
		return fn()
	}
	return cb.cb.Execute(fn)
}

// GetStats returns circuit breaker statistics
func (cb *AICircuitBreaker) GetStats() map[string]any {
	if cb == nil || cb.cb == nil {
		return map[string]any{
			"enabled": false,
		}
	}

	return map[string]any{
		"name":    cb.cb.Name(),
		"state":   cb.cb.State().String(),
		"counts":  cb.cb.Counts(),
		"enabled": true,
	}
}

// GetModelStats returns model circuit breaker statistics
func (cb *ModelCircuitBreaker) GetModelStats() map[string]any {
	if cb == nil || cb.cb == nil {
		return map[string]any{
			"enabled": false,
		}
	}

	return map[string]any{
		"name":    cb.cb.Name(),
		"state":   cb.cb.State().String(),
		"counts":  cb.cb.Counts(),
		"enabled": true,
	}
}

// IsHealthy returns true if the circuit breaker is in closed state
func (cb *AICircuitBreaker) IsHealthy() bool {
	if cb == nil || cb.cb == nil {
		return true // If no circuit breaker, consider it healthy
	}
	return cb.cb.State() == gobreaker.StateClosed
}

// IsModelHealthy returns true if the model circuit breaker is in closed state
func (cb *ModelCircuitBreaker) IsModelHealthy() bool {
	if cb == nil || cb.cb == nil {
		return true // If no circuit breaker, consider it healthy
	}
	return cb.cb.State() == gobreaker.StateClosed
}

// Reset resets the circuit breaker (note: gobreaker v2 doesn't have a public Reset method)
func (cb *AICircuitBreaker) Reset() {
	// Note: gobreaker v2 doesn't have a public Reset method
	// Circuit breakers will automatically reset based on their timeout settings
	// Note: Circuit breakers will automatically reset based on their timeout settings
	// No logger available in this context since it's a method on the breaker itself
}

// ResetModel resets the model circuit breaker
func (cb *ModelCircuitBreaker) ResetModel() {
	// Note: gobreaker v2 doesn't have a public Reset method
	// Circuit breakers will automatically reset based on their timeout settings
	// Note: Circuit breakers will automatically reset based on their timeout settings
	// No logger available in this context since it's a method on the breaker itself
}
